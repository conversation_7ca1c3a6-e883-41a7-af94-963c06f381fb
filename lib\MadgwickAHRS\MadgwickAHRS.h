//=============================================================================================
// MadgwickAHRS.h
//=============================================================================================
//
// Implementation of Madgwick's IMU and AHRS algorithms.
// See: http://www.x-io.co.uk/open-source-imu-and-ahrs-algorithms/
//
// From the x-io website "Open-source resources available on this website are
// provided under the GNU General Public Licence unless an alternative licence
// is provided in source."
//
// Date			Author          Notes
// 29/09/2011	SOH Madgwick    Initial release
// 02/10/2011	SOH Madgwick	Optimized for reduced CPU load
// 19/02/2012	SOH Madgwick	Magnetometer measurement is normalized
// 12/11/2019   Aster			Adapted for Arduino
// 03/08/2024   HyperBall       Adapted for HyperBallV1 firmware
//
//=============================================================================================
#ifndef MadgwickAHRS_h
#define MadgwickAHRS_h

#include <math.h>

class Madgwick {
private:
    static float invSqrt(float x);
    float beta;				// algorithm gain
    float invSampleFreq;
    float roll;
    float pitch;
    float yaw;
    char anglesComputed;
    void computeAngles();

public:
    // Raw quaternion values (public for direct access)
    float q0, q1, q2, q3;

    Madgwick(void);
    void begin(float sampleFrequency) { invSampleFreq = 1.0f / sampleFrequency; }
    void update(float gx, float gy, float gz, float ax, float ay, float az, float mx, float my, float mz);
    void updateIMU(float gx, float gy, float gz, float ax, float ay, float az);

    // Quaternion getters
    float getQ0() { return q0; }
    float getQ1() { return q1; }
    float getQ2() { return q2; }
    float getQ3() { return q3; }

    // Euler angle getters
    float getRoll() {
        if (!anglesComputed) computeAngles();
        return roll * 57.29578f;
    }
    float getPitch() {
        if (!anglesComputed) computeAngles();
        return pitch * 57.29578f;
    }
    float getYaw() {
        if (!anglesComputed) computeAngles();
        return yaw * 57.29578f;
    }

    // Algorithm gain setter
    void setBeta(float beta) { this->beta = beta; }
    float getBeta() { return beta; }
};

#endif
