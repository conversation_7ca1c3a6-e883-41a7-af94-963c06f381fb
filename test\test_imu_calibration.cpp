#include <Arduino.h>
#include <unity.h>
#include <Adafruit_LSM6DS.h>
#include <Adafruit_LSM6DS3.h>

Adafruit_LSM6DS3 lsm6ds;
float accel_offset[3] = {0, 0, 0};
float gyro_offset[3] = {0, 0, 0};

void setUp(void) {
  // Initialize IMU before each test
  lsm6ds.begin_I2C();
  lsm6ds.setAccelRange(LSM6DS_ACCEL_RANGE_8_G);
  lsm6ds.setGyroRange(LSM6DS_GYRO_RANGE_2000_DPS);
  lsm6ds.setAccelDataRate(LSM6DS_RATE_208_HZ);
  lsm6ds.setGyroDataRate(LSM6DS_RATE_208_HZ);
}

void tearDown(void) {
  // Clean up after each test
}

void test_imu_initialization(void) {
  // Test that IMU initializes correctly
  bool imu_initialized = lsm6ds.begin_I2C();
  TEST_ASSERT_TRUE(imu_initialized);
}

void test_imu_readings(void) {
  // Test that IMU provides readings
  sensors_event_t accel, gyro, temp;
  lsm6ds.getEvent(&accel, &gyro, &temp);

  // Check that acceleration values are reasonable (not zero)
  TEST_ASSERT_NOT_EQUAL(0.0f, accel.acceleration.x);
  TEST_ASSERT_NOT_EQUAL(0.0f, accel.acceleration.y);
  TEST_ASSERT_NOT_EQUAL(0.0f, accel.acceleration.z);

  // Z-axis should be approximately 9.8 m/s² due to gravity
  TEST_ASSERT_FLOAT_WITHIN(2.0f, 9.8f, accel.acceleration.z);
}

void test_calibration(void) {
  // Reset offsets
  for (int i = 0; i < 3; i++) {
    accel_offset[i] = 0;
    gyro_offset[i] = 0;
  }

  // Take samples for calibration
  const int num_samples = 100; // Reduced for testing
  for (int i = 0; i < num_samples; i++) {
    sensors_event_t accel, gyro, temp;
    lsm6ds.getEvent(&accel, &gyro, &temp);

    accel_offset[0] += accel.acceleration.x;
    accel_offset[1] += accel.acceleration.y;
    accel_offset[2] += accel.acceleration.z - 9.8; // Subtract gravity from z-axis

    gyro_offset[0] += gyro.gyro.x;
    gyro_offset[1] += gyro.gyro.y;
    gyro_offset[2] += gyro.gyro.z;

    delay(5);
  }

  // Calculate average offsets
  for (int i = 0; i < 3; i++) {
    accel_offset[i] /= num_samples;
    gyro_offset[i] /= num_samples;
  }

  // Check that gyro offsets are small (device at rest)
  TEST_ASSERT_FLOAT_WITHIN(0.5f, 0.0f, gyro_offset[0]);
  TEST_ASSERT_FLOAT_WITHIN(0.5f, 0.0f, gyro_offset[1]);
  TEST_ASSERT_FLOAT_WITHIN(0.5f, 0.0f, gyro_offset[2]);

  // Check that accel x and y offsets are small (device flat)
  TEST_ASSERT_FLOAT_WITHIN(0.5f, 0.0f, accel_offset[0]);
  TEST_ASSERT_FLOAT_WITHIN(0.5f, 0.0f, accel_offset[1]);
}

void setup() {
  // Wait for serial port to connect
  delay(2000);

  UNITY_BEGIN();
  RUN_TEST(test_imu_initialization);
  RUN_TEST(test_imu_readings);
  RUN_TEST(test_calibration);
  UNITY_END();
}

void loop() {
  // Nothing to do here
}
