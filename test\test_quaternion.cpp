#include <Arduino.h>
#include <unity.h>
#include <Adafruit_LSM6DS.h>
#include <Adafruit_LSM6DS3.h>
#include "MadgwickAHRS.h"

Adafruit_LSM6DS3 lsm6ds;
Madgwick madgwick;
float accel_offset[3] = {0, 0, 0};
float gyro_offset[3] = {0, 0, 0};

void setUp(void) {
  // Initialize IMU before each test
  lsm6ds.begin_I2C();
  lsm6ds.setAccelRange(LSM6DS_ACCEL_RANGE_8_G);
  lsm6ds.setGyroRange(LSM6DS_GYRO_RANGE_2000_DPS);
  lsm6ds.setAccelDataRate(LSM6DS_RATE_416_HZ);
  lsm6ds.setGyroDataRate(LSM6DS_RATE_416_HZ);

  // Initialize Madgwick filter
  madgwick.begin(416.0f);
  madgwick.setBeta(0.1f);
}

void tearDown(void) {
  // Clean up after each test
}

void test_madgwick_initialization(void) {
  // Test that Madgwick filter initializes with identity quaternion
  TEST_ASSERT_FLOAT_WITHIN(0.001, 1.0, madgwick.q0);
  TEST_ASSERT_FLOAT_WITHIN(0.001, 0.0, madgwick.q1);
  TEST_ASSERT_FLOAT_WITHIN(0.001, 0.0, madgwick.q2);
  TEST_ASSERT_FLOAT_WITHIN(0.001, 0.0, madgwick.q3);
}

void test_madgwick_update(void) {
  // Test that Madgwick filter updates with sensor data
  sensors_event_t accel, gyro, temp;
  lsm6ds.getEvent(&accel, &gyro, &temp);

  // Apply calibration offsets
  float accel_x = accel.acceleration.x - accel_offset[0];
  float accel_y = accel.acceleration.y - accel_offset[1];
  float accel_z = accel.acceleration.z - accel_offset[2];

  float gyro_x = gyro.gyro.x - gyro_offset[0];
  float gyro_y = gyro.gyro.y - gyro_offset[1];
  float gyro_z = gyro.gyro.z - gyro_offset[2];

  // Update Madgwick filter
  madgwick.updateIMU(gyro_x, gyro_y, gyro_z, accel_x, accel_y, accel_z);

  // Test that quaternion is normalized
  float quat_norm = sqrt(madgwick.q0*madgwick.q0 +
                         madgwick.q1*madgwick.q1 +
                         madgwick.q2*madgwick.q2 +
                         madgwick.q3*madgwick.q3);

  TEST_ASSERT_FLOAT_WITHIN(0.001, 1.0, quat_norm);
}

void test_tilt_calculation(void) {
  // Test tilt calculation from quaternion

  // Set quaternion to known values (45 degree tilt around X axis)
  float angle = 45.0 * PI / 180.0;
  madgwick.q0 = cos(angle/2);
  madgwick.q1 = sin(angle/2);
  madgwick.q2 = 0.0;
  madgwick.q3 = 0.0;

  // Calculate tilt using the same method as in calculate_metrics()
  float tilt_pitch = asin(-2.0 * (madgwick.q1 * madgwick.q3 - madgwick.q0 * madgwick.q2)) * 180.0 / PI;
  float tilt = abs(tilt_pitch);

  // Test that tilt is approximately 45 degrees
  TEST_ASSERT_FLOAT_WITHIN(1.0, 45.0, tilt);
}

void test_spin_axis_calculation(void) {
  // Test spin axis calculation from quaternion

  // Set quaternion to known values (90 degree rotation around Z axis)
  float angle = 90.0 * PI / 180.0;
  madgwick.q0 = cos(angle/2);
  madgwick.q1 = 0.0;
  madgwick.q2 = 0.0;
  madgwick.q3 = sin(angle/2);

  // Calculate spin axis using the same method as in calculate_metrics()
  float spinAxisX = 2 * (madgwick.q1*madgwick.q3 + madgwick.q0*madgwick.q2);
  float spinAxisY = 2 * (madgwick.q2*madgwick.q3 - madgwick.q0*madgwick.q1);
  float spinAxisZ = 1 - 2 * (madgwick.q1*madgwick.q1 + madgwick.q2*madgwick.q2);

  // Test that spin axis is approximately [0, -1, 0]
  TEST_ASSERT_FLOAT_WITHIN(0.1, 0.0, spinAxisX);
  TEST_ASSERT_FLOAT_WITHIN(0.1, -1.0, spinAxisY);
  TEST_ASSERT_FLOAT_WITHIN(0.1, 0.0, spinAxisZ);
}

void RUN_UNITY_TESTS() {
  UNITY_BEGIN();
  RUN_TEST(test_madgwick_initialization);
  RUN_TEST(test_madgwick_update);
  RUN_TEST(test_tilt_calculation);
  RUN_TEST(test_spin_axis_calculation);
  UNITY_END();
}

void setup() {
  // Wait for serial port to connect
  delay(2000);

  // Run tests
  RUN_UNITY_TESTS();
}

void loop() {
  // Nothing to do here
}
