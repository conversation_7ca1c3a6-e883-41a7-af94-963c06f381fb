/*
 * HyperBallV1 Serial Test
 * 
 * This simple sketch tests serial communication at different baud rates.
 * It will output messages at both 9600 and 115200 baud.
 */

void setup() {
  // Initialize serial at 9600 baud
  Serial.begin(9600);
  delay(1000);
  
  // Output test message at 9600 baud
  Serial.println("HyperBallV1 Serial Test - 9600 baud");
  Serial.println("If you can see this, serial communication at 9600 baud is working!");
  Serial.println("Switching to 115200 baud in 5 seconds...");
  
  // Wait 5 seconds
  for (int i = 5; i > 0; i--) {
    Serial.print("Switching in ");
    Serial.print(i);
    Serial.println(" seconds...");
    delay(1000);
  }
  
  // Switch to 115200 baud
  Serial.end();
  Serial.begin(115200);
  delay(1000);
  
  // Output test message at 115200 baud
  Serial.println("HyperBallV1 Serial Test - 115200 baud");
  Serial.println("If you can see this, serial communication at 115200 baud is working!");
  Serial.println("The test will now output a message every second.");
}

void loop() {
  // Output a message every second
  static unsigned long counter = 0;
  
  Serial.print("HyperBallV1 Serial Test - Message #");
  Serial.print(counter++);
  Serial.print(" - Millis: ");
  Serial.println(millis());
  
  delay(1000);
}
