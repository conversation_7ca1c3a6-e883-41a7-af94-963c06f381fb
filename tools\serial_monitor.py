#!/usr/bin/env python3
"""
Simple Serial Monitor for HyperBallV1
This script connects to a serial port and displays incoming data.
It allows you to change baud rate on the fly.
"""

import serial
import serial.tools.list_ports
import time
import sys
import os
import threading
import argparse
from datetime import datetime

def list_ports():
    """List all available serial ports"""
    ports = serial.tools.list_ports.comports()
    if not ports:
        print("No serial ports found.")
        return []
    
    print("\nAvailable ports:")
    for i, port in enumerate(ports):
        print(f"{i+1}. {port.device} - {port.description}")
    return [p.device for p in ports]

def monitor_serial(port, baud_rate=115200, timeout=1):
    """Monitor the specified serial port"""
    try:
        ser = serial.Serial(port, baud_rate, timeout=timeout)
        print(f"\nConnected to {port} at {baud_rate} baud")
        print("Press Ctrl+C to exit, or type 'baud RATE' to change baud rate")
        
        # Create a log file
        log_filename = f"serial_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        log_file = open(log_filename, "w")
        print(f"Logging to {log_filename}")
        
        # Function to read from serial port
        def read_serial():
            while True:
                if ser.in_waiting:
                    try:
                        line = ser.readline().decode('utf-8', errors='replace').strip()
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"[{timestamp}] {line}")
                        log_file.write(f"[{timestamp}] {line}\n")
                        log_file.flush()
                    except Exception as e:
                        print(f"Error reading serial: {e}")
                time.sleep(0.01)
        
        # Start reading thread
        read_thread = threading.Thread(target=read_serial, daemon=True)
        read_thread.start()
        
        # Main loop for commands
        while True:
            try:
                cmd = input()
                if cmd.lower().startswith('baud '):
                    try:
                        new_baud = int(cmd.split()[1])
                        ser.close()
                        ser = serial.Serial(port, new_baud, timeout=timeout)
                        print(f"Changed baud rate to {new_baud}")
                    except Exception as e:
                        print(f"Error changing baud rate: {e}")
                elif cmd.lower() == 'help':
                    print("Commands:")
                    print("  baud RATE - Change baud rate (e.g., baud 115200)")
                    print("  help      - Show this help")
                    print("  exit      - Exit the monitor")
                elif cmd.lower() == 'exit':
                    break
                else:
                    # Send the command to the device
                    ser.write((cmd + '\r\n').encode())
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
        
        ser.close()
        log_file.close()
        print(f"\nDisconnected from {port}")
        print(f"Log saved to {log_filename}")
        
    except serial.SerialException as e:
        print(f"Error opening serial port {port}: {e}")
    except KeyboardInterrupt:
        if 'ser' in locals() and ser.is_open:
            ser.close()
        if 'log_file' in locals() and not log_file.closed:
            log_file.close()
        print(f"\nDisconnected from {port}")
        if 'log_filename' in locals():
            print(f"Log saved to {log_filename}")

def main():
    parser = argparse.ArgumentParser(description='Serial Monitor for HyperBallV1')
    parser.add_argument('-p', '--port', help='Serial port to connect to')
    parser.add_argument('-b', '--baud', type=int, default=115200, 
                        help='Baud rate (default: 115200)')
    args = parser.parse_args()
    
    port = args.port
    
    if not port:
        ports = list_ports()
        if not ports:
            return
        
        if len(ports) == 1:
            port = ports[0]
            print(f"Automatically selecting the only available port: {port}")
        else:
            try:
                choice = int(input("\nSelect port number: "))
                if 1 <= choice <= len(ports):
                    port = ports[choice-1]
                else:
                    print("Invalid selection.")
                    return
            except ValueError:
                print("Invalid input.")
                return
    
    monitor_serial(port, args.baud)

if __name__ == "__main__":
    main()
